#!/usr/bin/env python3
"""
Tests for MAC Address Topic Format

Tests to verify that MQTT topics use the correct MAC address format.
"""

import unittest
from datetime import datetime

from backend.mqtt.mqtt_config import TopicConfig
from backend.mqtt.price_publisher import PriceChangeData


class TestMACAddressTopics(unittest.TestCase):
    """Test cases for MAC address topic formatting."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.topic_config = TopicConfig()
    
    def test_price_change_topic_format(self):
        """Test that price change topics use the correct MAC address format."""
        mac_address = "AA:BB:CC:DD:EE:FF"
        expected_topic = "price_change/AA:BB:CC:DD:EE:FF"
        
        actual_topic = self.topic_config.get_price_change_topic(mac_address)
        
        self.assertEqual(actual_topic, expected_topic)
    
    def test_get_mac_topic_method(self):
        """Test the generic get_mac_topic method."""
        pattern = "test/{mac}"
        mac_address = "11:22:33:44:55:66"
        expected_topic = "test/11:22:33:44:55:66"
        
        actual_topic = self.topic_config.get_mac_topic(pattern, mac_address)
        
        self.assertEqual(actual_topic, expected_topic)
    
    def test_price_change_data_with_mac(self):
        """Test PriceChangeData with MAC address."""
        mac_address = "AA:BB:CC:DD:EE:FF"
        price_data = PriceChangeData(
            site_id="site_123",
            device_mac=mac_address,
            new_price=150.0,
            currency="NGN",
            scheduled_time=datetime.now()
        )
        
        self.assertEqual(price_data.device_mac, mac_address)
        
        # Test that MAC is included in dictionary representation
        data_dict = price_data.to_dict()
        self.assertEqual(data_dict["device_mac"], mac_address)
    
    def test_topic_config_default_pattern(self):
        """Test that the default price change device pattern uses MAC format."""
        expected_pattern = "price_change/{mac}"
        
        self.assertEqual(self.topic_config.price_change_device, expected_pattern)
    
    def test_various_mac_formats(self):
        """Test various MAC address formats."""
        test_cases = [
            "AA:BB:CC:DD:EE:FF",
            "00:11:22:33:44:55",
            "FF:EE:DD:CC:BB:AA",
            "12:34:56:78:9A:BC"
        ]
        
        for mac in test_cases:
            with self.subTest(mac=mac):
                expected_topic = f"price_change/{mac}"
                actual_topic = self.topic_config.get_price_change_topic(mac)
                self.assertEqual(actual_topic, expected_topic)


if __name__ == '__main__':
    unittest.main()
