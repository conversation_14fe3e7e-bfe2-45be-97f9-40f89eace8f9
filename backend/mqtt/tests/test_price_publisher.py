#!/usr/bin/env python3
"""
Tests for Price Publisher

Tests for the MQTT price publisher component.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
import json

from backend.mqtt.price_publisher import PricePublisher, PriceChangeData
from backend.mqtt.mqtt_config import MQTTConfig, TopicConfig, MQTTClientConfig


class TestPriceChangeData(unittest.TestCase):
    """Test cases for PriceChangeData dataclass."""
    
    def test_price_change_data_creation(self):
        """Test creating PriceChangeData instance."""
        scheduled_time = datetime.now()
        data = PriceChangeData(
            site_id="site_123",
            new_price=150.75,
            product_name="Premium Gasoline",
            currency="NGN",
            scheduled_time=scheduled_time,
            device_mac="AA:BB:CC:DD:EE:FF"
        )
        
        self.assertEqual(data.site_id, "site_123")
        self.assertEqual(data.new_price, 150.75)
        self.assertEqual(data.product_name, "Premium Gasoline")
        self.assertEqual(data.currency, "NGN")
        self.assertEqual(data.scheduled_time, scheduled_time)
        self.assertEqual(data.device_mac, "AA:BB:CC:DD:EE:FF")
    
    def test_price_change_data_to_dict(self):
        """Test converting PriceChangeData to dictionary."""
        scheduled_time = datetime(2023, 12, 25, 10, 30, 0)
        data = PriceChangeData(
            site_id="site_123",
            new_price=150.75,
            currency="NGN",
            scheduled_time=scheduled_time
        )
        
        result = data.to_dict()
        
        self.assertEqual(result["site_id"], "site_123")
        self.assertEqual(result["new_price"], 150.75)
        self.assertEqual(result["currency"], "NGN")
        self.assertEqual(result["scheduled_time"], "2023-12-25T10:30:00")
        self.assertIsNone(result["product_name"])
        self.assertIsNone(result["device_mac"])
    
    def test_price_change_data_to_json(self):
        """Test converting PriceChangeData to JSON."""
        scheduled_time = datetime(2023, 12, 25, 10, 30, 0)
        data = PriceChangeData(
            site_id="site_123",
            new_price=150.75,
            currency="NGN",
            scheduled_time=scheduled_time
        )
        
        json_str = data.to_json()
        parsed = json.loads(json_str)
        
        self.assertEqual(parsed["site_id"], "site_123")
        self.assertEqual(parsed["new_price"], 150.75)
        self.assertEqual(parsed["scheduled_time"], "2023-12-25T10:30:00")


class TestPricePublisher(unittest.TestCase):
    """Test cases for PricePublisher."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mqtt_config = MQTTConfig(
            host="test-broker",
            port=1883,
            username="test_user",
            password="test_pass"
        )
        self.topic_config = TopicConfig()
        self.client_config = MQTTClientConfig.get_default()
        
        # Create publisher with mocked MQTT client
        with patch('paho.mqtt.client.Client'):
            self.publisher = PricePublisher(
                self.mqtt_config,
                self.topic_config,
                self.client_config
            )
            self.publisher.client = Mock()
    
    def test_publisher_initialization(self):
        """Test publisher initialization."""
        self.assertIsNotNone(self.publisher.client)
        self.assertIsNotNone(self.publisher.logger)
        self.assertEqual(self.publisher.messages_sent, 0)
        self.assertEqual(self.publisher.retry_attempts, 0)
    
    def test_publish_price_update_success(self):
        """Test successful price update publishing."""
        # Mock successful publish
        self.publisher.client.publish.return_value.rc = 0
        self.publisher.is_connected = True
        
        price_data = PriceChangeData(
            site_id="site_123",
            new_price=150.0,
            currency="NGN",
            scheduled_time=datetime.now()
        )
        
        result = self.publisher.publish_price_update(price_data)
        
        self.assertTrue(result)
        self.assertEqual(self.publisher.messages_sent, 1)
        self.publisher.client.publish.assert_called_once()
    
    def test_publish_price_update_not_connected(self):
        """Test publishing when not connected."""
        self.publisher.is_connected = False
        
        price_data = PriceChangeData(
            site_id="site_123",
            new_price=150.0,
            currency="NGN",
            scheduled_time=datetime.now()
        )
        
        result = self.publisher.publish_price_update(price_data)
        
        self.assertFalse(result)
        self.assertEqual(self.publisher.messages_sent, 0)
    
    def test_publish_price_update_with_retry(self):
        """Test publishing with retry logic."""
        # Mock failed first attempt, successful second
        self.publisher.client.publish.side_effect = [
            Mock(rc=1),  # First attempt fails
            Mock(rc=0)   # Second attempt succeeds
        ]
        self.publisher.is_connected = True
        
        price_data = PriceChangeData(
            site_id="site_123",
            new_price=150.0,
            currency="NGN",
            scheduled_time=datetime.now()
        )
        
        with patch('time.sleep'):  # Mock sleep to speed up test
            result = self.publisher.publish_price_update(price_data, use_retry=True)
        
        self.assertTrue(result)
        self.assertEqual(self.publisher.client.publish.call_count, 2)
    
    def test_publish_device_price_update(self):
        """Test publishing price update for specific device."""
        self.publisher.client.publish.return_value.rc = 0
        self.publisher.is_connected = True
        
        price_data = PriceChangeData(
            site_id="site_123",
            new_price=150.0,
            currency="NGN",
            scheduled_time=datetime.now()
        )
        
        result = self.publisher.publish_device_price_update(price_data, "device_123")
        
        self.assertTrue(result)
        # Verify the topic includes device ID
        call_args = self.publisher.client.publish.call_args
        topic = call_args[0][0]
        self.assertIn("device_123", topic)
    
    def test_publish_site_price_updates(self):
        """Test publishing price updates for multiple devices in a site."""
        self.publisher.client.publish.return_value.rc = 0
        self.publisher.is_connected = True
        
        price_data_list = [
            PriceChangeData(
                site_id="site_123",
                new_price=150.0,
                currency="NGN",
                scheduled_time=datetime.now()
            ),
            PriceChangeData(
                site_id="site_123",
                new_price=155.0,
                currency="NGN",
                scheduled_time=datetime.now()
            )
        ]
        
        results = self.publisher.publish_site_price_updates(price_data_list, "site_123")
        
        self.assertEqual(len(results), 2)
        self.assertTrue(all(results.values()))
        self.assertEqual(self.publisher.client.publish.call_count, 3)  # 2 individual + 1 site-wide
    
    def test_get_status(self):
        """Test getting publisher status."""
        self.publisher.messages_sent = 10
        self.publisher.retry_attempts = 2
        
        status = self.publisher.get_status()
        
        self.assertEqual(status["messages_sent"], 10)
        self.assertEqual(status["retry_attempts"], 2)
        self.assertIn("last_publish_time", status)
    
    def test_create_for_site(self):
        """Test creating publisher for specific site."""
        with patch('paho.mqtt.client.Client'):
            publisher = PricePublisher.create_for_site("site_123")
            
            self.assertIsNotNone(publisher)
            self.assertIn("site_123", publisher.client_id)
    
    def test_create_for_device(self):
        """Test creating publisher for specific device."""
        with patch('paho.mqtt.client.Client'):
            publisher = PricePublisher.create_for_device("device_123")
            
            self.assertIsNotNone(publisher)
            self.assertIn("device_123", publisher.client_id)


if __name__ == '__main__':
    unittest.main()
