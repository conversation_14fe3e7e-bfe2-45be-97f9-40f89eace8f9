#!/usr/bin/env python3
"""
MQTT Test Runner

Script to run all MQTT tests with proper setup and reporting.
"""

import sys
import os
import unittest
import logging
from io import String<PERSON>

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

def setup_test_environment():
    """Setup test environment and logging."""
    # Disable MQTT logging during tests to reduce noise
    logging.getLogger("mqtt").setLevel(logging.CRITICAL)
    
    # Set test environment variables
    os.environ['MQTT_HOST'] = 'test-broker'
    os.environ['MQTT_PORT'] = '1883'
    os.environ['MQTT_USERNAME'] = 'test_user'
    os.environ['MQTT_PASSWORD'] = 'test_pass'
    os.environ['ENVIRONMENT'] = 'test'

def discover_and_run_tests():
    """Discover and run all MQTT tests."""
    # Setup test environment
    setup_test_environment()
    
    # Discover tests in the current directory
    loader = unittest.TestLoader()
    start_dir = os.path.dirname(__file__)
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # Create test runner with detailed output
    stream = StringIO()
    runner = unittest.TextTestRunner(
        stream=stream,
        verbosity=2,
        buffer=True,
        failfast=False
    )
    
    # Run tests
    print("🧪 Running MQTT Tests...")
    print("=" * 60)
    
    result = runner.run(suite)
    
    # Print results
    output = stream.getvalue()
    print(output)
    
    # Summary
    print("=" * 60)
    print(f"📊 Test Summary:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    print(f"   Skipped: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    
    if result.failures:
        print(f"\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\n💥 Errors:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    if result.wasSuccessful():
        print(f"\n✅ All tests passed!")
        return 0
    else:
        print(f"\n❌ Some tests failed!")
        return 1

def run_specific_test(test_name):
    """Run a specific test module or test case."""
    setup_test_environment()
    
    try:
        # Load specific test
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromName(test_name)
        
        # Run test
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return 0 if result.wasSuccessful() else 1
        
    except Exception as e:
        print(f"Error running test {test_name}: {e}")
        return 1

def main():
    """Main entry point."""
    if len(sys.argv) > 1:
        # Run specific test
        test_name = sys.argv[1]
        return run_specific_test(test_name)
    else:
        # Run all tests
        return discover_and_run_tests()

if __name__ == '__main__':
    sys.exit(main())
