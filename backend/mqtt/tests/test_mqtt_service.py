#!/usr/bin/env python3
"""
Tests for MQTT Service Manager

Comprehensive tests for the MQTT service manager and related components.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import threading
import time
from datetime import datetime

from backend.mqtt.mqtt_service import MQTTService, ServiceState, MQTTServiceConfig
from backend.mqtt.mqtt_config import MQTTConfig, TopicConfig, MQTTClientConfig
from backend.mqtt.price_publisher import PriceChangeData


class TestMQTTService(unittest.TestCase):
    """Test cases for MQTT Service Manager."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mqtt_config = MQTTConfig(
            host="test-broker",
            port=1883,
            username="test_user",
            password="test_pass"
        )
        
        self.topic_config = TopicConfig()
        self.client_config = MQTTClientConfig.get_default()
        self.service_config = MQTTServiceConfig(
            auto_start_publisher=True,
            auto_start_subscriber=False,
            enable_health_check=False  # Disable for tests
        )
        
        self.service = MQTTService(
            mqtt_config=self.mqtt_config,
            topic_config=self.topic_config,
            client_config=self.client_config,
            service_config=self.service_config
        )
    
    def test_service_initialization(self):
        """Test service initialization."""
        self.assertEqual(self.service.state, ServiceState.STOPPED)
        self.assertIsNone(self.service.publisher)
        self.assertIsNone(self.service.subscriber)
        self.assertIsNotNone(self.service.logger)
    
    @patch('backend.mqtt.price_publisher.PricePublisher')
    def test_start_service_publisher_only(self, mock_publisher_class):
        """Test starting service with publisher only."""
        # Mock publisher
        mock_publisher = Mock()
        mock_publisher.connect.return_value = True
        mock_publisher.is_connected = True
        mock_publisher_class.return_value = mock_publisher
        
        # Start service
        result = self.service.start()
        
        # Assertions
        self.assertTrue(result)
        self.assertEqual(self.service.state, ServiceState.RUNNING)
        self.assertIsNotNone(self.service.publisher)
        self.assertIsNone(self.service.subscriber)
        mock_publisher.connect.assert_called_once()
    
    @patch('backend.mqtt.price_publisher.PricePublisher')
    def test_start_service_publisher_connection_fails(self, mock_publisher_class):
        """Test service start when publisher connection fails."""
        # Mock publisher with failed connection
        mock_publisher = Mock()
        mock_publisher.connect.return_value = False
        mock_publisher_class.return_value = mock_publisher
        
        # Start service
        result = self.service.start()
        
        # Assertions
        self.assertFalse(result)
        self.assertEqual(self.service.state, ServiceState.ERROR)
    
    @patch('backend.mqtt.price_subscriber.PriceSubscriber')
    @patch('backend.mqtt.price_publisher.PricePublisher')
    def test_start_service_with_subscriber(self, mock_publisher_class, mock_subscriber_class):
        """Test starting service with both publisher and subscriber."""
        # Enable subscriber in config
        self.service.service_config.auto_start_subscriber = True
        
        # Mock publisher and subscriber
        mock_publisher = Mock()
        mock_publisher.connect.return_value = True
        mock_publisher.is_connected = True
        mock_publisher_class.return_value = mock_publisher
        
        mock_subscriber = Mock()
        mock_subscriber.connect.return_value = True
        mock_subscriber.is_connected = True
        mock_subscriber_class.return_value = mock_subscriber
        
        # Start service
        result = self.service.start()
        
        # Assertions
        self.assertTrue(result)
        self.assertEqual(self.service.state, ServiceState.RUNNING)
        self.assertIsNotNone(self.service.publisher)
        self.assertIsNotNone(self.service.subscriber)
    
    @patch('backend.mqtt.price_publisher.PricePublisher')
    def test_stop_service(self, mock_publisher_class):
        """Test stopping the service."""
        # Mock publisher
        mock_publisher = Mock()
        mock_publisher.connect.return_value = True
        mock_publisher.is_connected = True
        mock_publisher_class.return_value = mock_publisher
        
        # Start and then stop service
        self.service.start()
        self.service.stop()
        
        # Assertions
        self.assertEqual(self.service.state, ServiceState.STOPPED)
        self.assertIsNone(self.service.publisher)
        mock_publisher.disconnect.assert_called_once()
    
    @patch('backend.mqtt.price_publisher.PricePublisher')
    def test_publish_price_update(self, mock_publisher_class):
        """Test publishing price update through service."""
        # Mock publisher
        mock_publisher = Mock()
        mock_publisher.connect.return_value = True
        mock_publisher.is_connected = True
        mock_publisher.publish_price_update.return_value = True
        mock_publisher_class.return_value = mock_publisher
        
        # Start service
        self.service.start()
        
        # Create test price data
        price_data = PriceChangeData(
            site_id="test_site",
            new_price=150.0,
            currency="NGN",
            scheduled_time=datetime.now()
        )
        
        # Publish price update
        result = self.service.publish_price_update(price_data)
        
        # Assertions
        self.assertTrue(result)
        mock_publisher.publish_price_update.assert_called_once_with(price_data, True)
    
    def test_publish_price_update_no_publisher(self):
        """Test publishing when no publisher is available."""
        price_data = PriceChangeData(
            site_id="test_site",
            new_price=150.0,
            currency="NGN",
            scheduled_time=datetime.now()
        )
        
        # Try to publish without starting service
        result = self.service.publish_price_update(price_data)
        
        # Should fail
        self.assertFalse(result)
    
    @patch('backend.mqtt.price_publisher.PricePublisher')
    def test_get_status(self, mock_publisher_class):
        """Test getting service status."""
        # Mock publisher
        mock_publisher = Mock()
        mock_publisher.connect.return_value = True
        mock_publisher.is_connected = True
        mock_publisher.get_status.return_value = {"messages_sent": 5}
        mock_publisher_class.return_value = mock_publisher
        
        # Start service
        self.service.start()
        
        # Get status
        status = self.service.get_status()
        
        # Assertions
        self.assertEqual(status["service_state"], ServiceState.RUNNING.value)
        self.assertTrue(status["publisher_connected"])
        self.assertFalse(status["subscriber_connected"])
        self.assertEqual(status["broker"], "test-broker:1883")
        self.assertIn("publisher_status", status)
    
    def test_context_manager(self):
        """Test using service as context manager."""
        with patch('backend.mqtt.price_publisher.PricePublisher') as mock_publisher_class:
            mock_publisher = Mock()
            mock_publisher.connect.return_value = True
            mock_publisher.is_connected = True
            mock_publisher_class.return_value = mock_publisher
            
            with self.service as service:
                self.assertEqual(service.state, ServiceState.RUNNING)
            
            # Should be stopped after context exit
            self.assertEqual(self.service.state, ServiceState.STOPPED)
    
    def test_create_default(self):
        """Test creating service with default configuration."""
        service = MQTTService.create_default()
        
        self.assertIsNotNone(service.mqtt_config)
        self.assertIsNotNone(service.topic_config)
        self.assertIsNotNone(service.client_config)
        self.assertIsNotNone(service.service_config)
        self.assertEqual(service.state, ServiceState.STOPPED)


class TestMQTTServiceConfig(unittest.TestCase):
    """Test cases for MQTT Service Configuration."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = MQTTServiceConfig.get_default()
        
        self.assertTrue(config.auto_start_publisher)
        self.assertFalse(config.auto_start_subscriber)
        self.assertTrue(config.enable_health_check)
        self.assertEqual(config.health_check_interval, 30)
        self.assertEqual(config.max_connection_retries, 3)
    
    def test_custom_config(self):
        """Test custom configuration values."""
        config = MQTTServiceConfig(
            auto_start_publisher=False,
            auto_start_subscriber=True,
            enable_health_check=False,
            health_check_interval=60,
            max_connection_retries=5
        )
        
        self.assertFalse(config.auto_start_publisher)
        self.assertTrue(config.auto_start_subscriber)
        self.assertFalse(config.enable_health_check)
        self.assertEqual(config.health_check_interval, 60)
        self.assertEqual(config.max_connection_retries, 5)


if __name__ == '__main__':
    unittest.main()
