#!/usr/bin/env python3
"""
MQTT Logging Configuration

Centralized logging configuration for MQTT components.
"""

import logging
import logging.handlers
import os
from typing import Optional
from datetime import datetime


class MQTTLogFormatter(logging.Formatter):
    """Custom formatter for MQTT logs with enhanced context."""
    
    def __init__(self):
        super().__init__()
        
    def format(self, record):
        # Add timestamp
        record.timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        
        # Add client context if available
        client_id = getattr(record, 'client_id', 'unknown')
        
        # Create formatted message
        if record.levelno >= logging.ERROR:
            emoji = "❌"
        elif record.levelno >= logging.WARNING:
            emoji = "⚠️"
        elif record.levelno >= logging.INFO:
            emoji = "ℹ️"
        else:
            emoji = "🔍"
            
        format_str = f"{emoji} {record.timestamp} - {record.name} - {record.levelname} - [{client_id}] {record.getMessage()}"
        
        # Add exception info if present
        if record.exc_info:
            format_str += f"\n{self.formatException(record.exc_info)}"
            
        return format_str


def setup_mqtt_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    enable_console: bool = True
) -> None:
    """
    Setup comprehensive logging for MQTT components.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional log file path
        max_file_size: Maximum size of log file before rotation
        backup_count: Number of backup files to keep
        enable_console: Whether to enable console logging
    """
    
    # Convert string level to logging constant
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Create custom formatter
    formatter = MQTTLogFormatter()
    
    # Get root logger for MQTT
    mqtt_logger = logging.getLogger("mqtt")
    mqtt_logger.setLevel(numeric_level)
    
    # Clear existing handlers
    mqtt_logger.handlers.clear()
    
    # Console handler
    if enable_console:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(formatter)
        mqtt_logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file:
        # Ensure log directory exists
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
            
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(formatter)
        mqtt_logger.addHandler(file_handler)
    
    # Prevent propagation to root logger to avoid duplicate messages
    mqtt_logger.propagate = False
    
    # Log initial setup message
    mqtt_logger.info(f"MQTT logging initialized - Level: {log_level}, File: {log_file or 'None'}")


def get_mqtt_logger(name: str) -> logging.Logger:
    """
    Get a logger for MQTT components.
    
    Args:
        name: Logger name (will be prefixed with 'mqtt.')
        
    Returns:
        Configured logger instance
    """
    return logging.getLogger(f"mqtt.{name}")


class MQTTLogContext:
    """Context manager for adding client context to log messages."""
    
    def __init__(self, logger: logging.Logger, client_id: str):
        self.logger = logger
        self.client_id = client_id
        self.old_factory = logging.getLogRecordFactory()
        
    def __enter__(self):
        def record_factory(*args, **kwargs):
            record = self.old_factory(*args, **kwargs)
            record.client_id = self.client_id
            return record
        logging.setLogRecordFactory(record_factory)
        return self.logger
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        logging.setLogRecordFactory(self.old_factory)


def log_mqtt_event(logger: logging.Logger, level: int, event: str, **kwargs) -> None:
    """
    Log an MQTT event with structured data.
    
    Args:
        logger: Logger instance
        level: Log level
        event: Event description
        **kwargs: Additional context data
    """
    context_str = ", ".join(f"{k}={v}" for k, v in kwargs.items())
    message = f"{event}"
    if context_str:
        message += f" ({context_str})"
    logger.log(level, message)


def log_connection_event(logger: logging.Logger, client_id: str, broker: str, success: bool, error: Optional[str] = None) -> None:
    """Log connection events with standardized format."""
    if success:
        log_mqtt_event(logger, logging.INFO, "Connection established", 
                      client_id=client_id, broker=broker)
    else:
        log_mqtt_event(logger, logging.ERROR, "Connection failed", 
                      client_id=client_id, broker=broker, error=error)


def log_publish_event(logger: logging.Logger, client_id: str, topic: str, success: bool, payload_size: int, error: Optional[str] = None) -> None:
    """Log publish events with standardized format."""
    if success:
        log_mqtt_event(logger, logging.INFO, "Message published", 
                      client_id=client_id, topic=topic, size=payload_size)
    else:
        log_mqtt_event(logger, logging.ERROR, "Publish failed", 
                      client_id=client_id, topic=topic, size=payload_size, error=error)


def log_subscribe_event(logger: logging.Logger, client_id: str, topic: str, success: bool, error: Optional[str] = None) -> None:
    """Log subscription events with standardized format."""
    if success:
        log_mqtt_event(logger, logging.INFO, "Subscribed to topic", 
                      client_id=client_id, topic=topic)
    else:
        log_mqtt_event(logger, logging.ERROR, "Subscription failed", 
                      client_id=client_id, topic=topic, error=error)


def log_message_received(logger: logging.Logger, client_id: str, topic: str, payload_size: int) -> None:
    """Log received message events."""
    log_mqtt_event(logger, logging.INFO, "Message received", 
                  client_id=client_id, topic=topic, size=payload_size)


# Default logging setup for development
def setup_development_logging():
    """Setup logging for development environment."""
    setup_mqtt_logging(
        log_level="DEBUG",
        log_file="logs/mqtt_dev.log",
        enable_console=True
    )


# Default logging setup for production
def setup_production_logging():
    """Setup logging for production environment."""
    setup_mqtt_logging(
        log_level="INFO",
        log_file="logs/mqtt_prod.log",
        enable_console=False,
        max_file_size=50 * 1024 * 1024,  # 50MB
        backup_count=10
    )


# Auto-setup based on environment
def auto_setup_logging():
    """Automatically setup logging based on environment."""
    env = os.getenv('ENVIRONMENT', 'development').lower()
    
    if env == 'production':
        setup_production_logging()
    else:
        setup_development_logging()


# Initialize logging when module is imported
if not logging.getLogger("mqtt").handlers:
    auto_setup_logging()
