#!/usr/bin/env python3
"""
MQTT Price Publisher

A robust MQTT publisher for sending price change notifications.
Includes proper error handling, logging, and structured message support.
"""

import sys
import json
import time
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

import paho.mqtt.client as mqtt

from .mqtt_base import BaseMQTTClient
from .mqtt_config import MQ<PERSON>Config, TopicConfig, MQTTClientConfig


@dataclass
class PriceChangeData:
    """Data structure for price change notifications."""
    site_id: str
    device_id: Optional[str] = None
    device_mac: Optional[str] = None
    new_price: float = 0.0
    old_price: Optional[float] = None
    product_name: Optional[str] = None
    currency: str = "NGN"
    timestamp: Optional[datetime] = None
    scheduled_time: Optional[datetime] = None
    additional_data: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        data = {
            "site_id": self.site_id,
            "new_price": self.new_price,
            "currency": self.currency,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
        }

        if self.device_id:
            data["device_id"] = self.device_id
        if self.device_mac:
            data["device_mac"] = self.device_mac
        if self.old_price is not None:
            data["old_price"] = self.old_price
        if self.product_name:
            data["product_name"] = self.product_name
        if self.scheduled_time:
            data["scheduled_time"] = self.scheduled_time.isoformat()
        if self.additional_data:
            data.update(self.additional_data)

        return data


class PricePublisher(BaseMQTTClient):
    """MQTT publisher for price change notifications."""

    def __init__(self,
                 config: Optional[MQTTConfig] = None,
                 topic_config: Optional[TopicConfig] = None,
                 client_config: Optional[MQTTClientConfig] = None):
        # Use default configurations if not provided
        mqtt_config = config or MQTTConfig.from_env()
        client_cfg = client_config or MQTTClientConfig.get_default()

        super().__init__(mqtt_config, client_cfg, client_id="price_publisher")
        self.topic_config = topic_config or TopicConfig()

    def _on_connect_success(self, client: mqtt.Client) -> None:
        """Called when connection is successful."""
        del client  # Explicitly delete to avoid unused variable warning
        self._log_with_context(
            logging.INFO, "Price publisher connected and ready to send messages")

    def publish_price_update(self, price_data: PriceChangeData, use_retry: bool = True) -> bool:
        """
        Publish a structured price update message.

        Args:
            price_data: PriceChangeData object containing price information
            use_retry: Whether to use retry logic for publishing

        Returns:
            True if message was published successfully, False otherwise
        """
        try:
            message_json = json.dumps(price_data.to_dict(), indent=2)
            topic = self.topic_config.get_mac_topic(
                self.topic_config.price_change_device, price_data.device_mac)
            qos = self.topic_config.qos_important  # Use higher QoS for price changes

            if use_retry:
                return self.publish_with_retry(topic, message_json, qos)
            else:
                return self.publish(topic, message_json, qos)

        except Exception as e:
            self._log_with_context(
                logging.ERROR, f"Error creating price update message: {e}")
            return False

    def publish_device_price_update(self, price_data: PriceChangeData, device_id: str, use_retry: bool = True) -> bool:
        """
        Publish a price update to a specific device.

        Args:
            price_data: PriceChangeData object containing price information
            device_id: Device ID or MAC address
            use_retry: Whether to use retry logic for publishing

        Returns:
            True if message was published successfully, False otherwise
        """
        try:
            message_json = json.dumps(price_data.to_dict(), indent=2)
            # Check if the identifier looks like a MAC address (contains colons)
            if ':' in device_id:
                # Use MAC address format: price_change/AA:BB:CC:DD:EE:FF
                topic = self.topic_config.get_price_change_topic(device_id)
            else:
                # Fallback to device ID format for backward compatibility
                topic = self.topic_config.get_device_topic(
                    self.topic_config.price_change_device, device_id)
            qos = self.topic_config.qos_important

            if use_retry:
                return self.publish_with_retry(topic, message_json, qos)
            else:
                return self.publish(topic, message_json, qos)

        except Exception as e:
            self._log_with_context(
                logging.ERROR, f"Error publishing device price update: {e}")
            return False

    def publish_site_price_updates(self, price_data_list: List[PriceChangeData], site_id: str) -> Dict[str, bool]:
        """
        Publish price updates for multiple devices in a site.

        Args:
            price_data_list: List of PriceChangeData objects
            site_id: Site ID for notifications

        Returns:
            Dictionary mapping device_id to success status
        """
        results = {}

        # Publish to site notification topic
        site_topic = self.topic_config.get_site_topic(
            self.topic_config.site_notifications, site_id)

        site_message = {
            "type": "price_update",
            "site_id": site_id,
            "timestamp": datetime.now().isoformat(),
            "device_count": len(price_data_list),
            "updates": [data.to_dict() for data in price_data_list]
        }

        site_success = self.publish_with_retry(
            site_topic,
            json.dumps(site_message, indent=2),
            self.topic_config.qos_important
        )
        results["site_notification"] = site_success

        # Publish to individual devices (prioritize MAC address over device ID)
        for price_data in price_data_list:
            if price_data.device_mac:
                device_success = self.publish_device_price_update(
                    price_data, price_data.device_mac)
                results[price_data.device_mac] = device_success
            elif price_data.device_id:
                device_success = self.publish_device_price_update(
                    price_data, price_data.device_id)
                results[price_data.device_id] = device_success

        return results

    def publish_simple_price_message(self, message: str, topic: Optional[str] = None) -> bool:
        """
        Publish a simple text price message.

        Args:
            message: The message text to publish
            topic: Optional custom topic (defaults to price_changes)

        Returns:
            True if message was published successfully, False otherwise
        """
        target_topic = topic or self.topic_config.price_changes
        return self.publish_with_retry(target_topic, message, self.topic_config.qos)

    def publish_test_message(self, message: str = "Test message from price publisher") -> bool:
        """
        Publish a test message to the test topic.

        Args:
            message: The test message to publish

        Returns:
            True if message was published successfully, False otherwise
        """
        return self.publish(self.topic_config.test_topic, message, self.topic_config.qos)

    @classmethod
    def create_default(cls) -> 'PricePublisher':
        """Create a PricePublisher with default configuration."""
        return cls()

    @classmethod
    def create_with_config(cls, mqtt_config: MQTTConfig, topic_config: TopicConfig) -> 'PricePublisher':
        """Create a PricePublisher with custom configuration."""
        return cls(mqtt_config, topic_config)


def main():
    """Main entry point for the price publisher."""
    publisher = PricePublisher.create_default()

    if not publisher.connect():
        print("Failed to connect to MQTT broker")
        sys.exit(1)

    try:
        # Example usage - publish different types of messages
        print("Publishing price updates...")

        # Create sample price data
        price_data = PriceChangeData(
            site_id="SITE001",
            device_id="DEV001",
            new_price=150.50,
            old_price=145.00,
            product_name="PMS",
            currency="NGN"
        )

        # Publish a structured price update
        success = publisher.publish_price_update(price_data)
        if success:
            print("✅ Published structured price update")
        else:
            print("❌ Failed to publish structured price update")

        time.sleep(1)

        # Publish a simple text message
        success = publisher.publish_simple_price_message(
            "Price change notification: PMS now ₦150.50"
        )
        if success:
            print("✅ Published simple price message")
        else:
            print("❌ Failed to publish simple price message")

        time.sleep(1)

        # Publish a test message
        success = publisher.publish_test_message()
        if success:
            print("✅ Published test message")
        else:
            print("❌ Failed to publish test message")

    except KeyboardInterrupt:
        print("\nReceived keyboard interrupt")
    except Exception as e:
        print(f"Error during publishing: {e}")
    finally:
        publisher.disconnect()
        print("Disconnected from MQTT broker")


if __name__ == "__main__":
    main()
