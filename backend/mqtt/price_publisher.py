#!/usr/bin/env python3
"""
MQTT Price Publisher

A robust MQTT publisher for sending price change notifications.
Includes proper error handling, logging, and structured message support.
"""

import sys
import json
import time
from datetime import datetime
from backend.devices.utils import get_device_mac_address
from typing import Dict, Any, Optional

import paho.mqtt.client as mqtt

from mqtt_base import BaseMQTTClient
from mqtt_config import MQTTConfig, TopicConfig


class PricePublisher(BaseMQTTClient):
    """MQTT publisher for price change notifications."""

    def __init__(self, config: MQTTConfig, topic_config: TopicConfig):
        super().__init__(config, client_id="price_publisher")
        self.topic_config = topic_config

    def _on_connect_success(self, client: mqtt.Client) -> None:
        """Called when connection is successful."""
        self.logger.info(
            "Price publisher connected and ready to send messages")

    def publish_price_update(self, data: Dict[str, Any]) -> bool:
        """
        Publish a structured price update message.

        Args:
            price: The price value
            currency: Currency code (default: USD)
            symbol: Trading symbol or asset name
            additional_data: Additional data to include in the message

        Returns:
            True if message was published successfully, False otherwise
        """
        try:
            # message_data = {
            #     "price": data.price,
            #     "currency": currency,
            #     "timestamp": datetime.now().isoformat(),
            # }

            # if symbol:
            #     message_data["symbol"] = symbol

            # if additional_data:
            #     message_data.update(additional_data)

            message_json = json.dumps(data, indent=2)

            return self.publish(
                "self.topic_config.price_changes",
                message_json,
                self.topic_config.qos
            )

        except Exception as e:
            self.logger.error(f"Error creating price update message: {e}")
            return False

    def publish_simple_price_message(self, message: str) -> bool:
        """
        Publish a simple text price message.

        Args:
            message: The message text to publish

        Returns:
            True if message was published successfully, False otherwise
        """
        return self.publish(
            "self.topic_config.price_changes",
            message,
            self.topic_config.qos
        )

    def publish_test_message(self, message: str = "Test message from price publisher") -> bool:
        """
        Publish a test message to the test topic.

        Args:
            message: The test message to publish

        Returns:
            True if message was published successfully, False otherwise
        """
        return self.publish(
            "self.topic_config.test_topic",
            message,
            self.topic_config.qos
        )


def main():
    """Main entry point for the price publisher."""
    config = MQTTConfig.from_env()
    topic_config = TopicConfig()
    publisher = PricePublisher(config, topic_config)

    if not publisher.connect():
        print("Failed to connect to MQTT broker")
        sys.exit(1)

    # Wait a moment for connection to establish
    time.sleep(1)

    # try:
    #     # Example usage - publish different types of messages
    #     print("Publishing price updates...")

    # Publish a structured price update
    #     success = publisher.publish_price_update(
    #         price=5000.50,
    #         currency="USD",
    #         symbol="BTC",
    #         additional_data={"exchange": "example", "volume": 1.5}
    #     )

    #     if success:
    #         print("✅ Published structured price update")
    #     else:
    #         print("❌ Failed to publish structured price update")

    #     time.sleep(1)

    #     # Publish a simple text message
    #     success = publisher.publish_simple_price_message(
    #         "Hi, here is the price change! 5000"
    #     )

    #     if success:
    #         print("✅ Published simple price message")
    #     else:
    #         print("❌ Failed to publish simple price message")

    #     time.sleep(1)

    #     # Publish a test message
    #     success = publisher.publish_test_message()

    #     if success:
    #         print("✅ Published test message")
    #     else:
    #         print("❌ Failed to publish test message")

    # except KeyboardInterrupt:
    #     print("\nReceived keyboard interrupt")
    # except Exception as e:
    #     print(f"Error during publishing: {e}")
    # finally:
    #     publisher.disconnect()
    #     print("Disconnected from MQTT broker")


if __name__ == "__main__":
    main()
