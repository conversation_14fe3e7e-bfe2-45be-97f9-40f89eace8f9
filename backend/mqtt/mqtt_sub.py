import sys
from decouple import config
import paho.mqtt.client as paho


def message_handling(client, userdata, msg):
    print(f"{msg.topic}: {msg.payload.decode()}")


client = paho.Client()
client.on_message = message_handling

if client.connect(config("MQTT_CONNECTION_URL"), config("MQTT_CONNECTION_PORT", default=1883, cast=int), config("MQTT_CONNECTION_KEEPALIVE", default=60, cast=int)) != 0:
    print("Couldn't connect to the mqtt broker")
    sys.exit(1)

client.subscribe("price_changes")

try:
    print("Press CTRL+C to exit...")
    client.loop_forever()
except Exception:
    print("Caught an Exception, something went wrong...")
finally:
    print("Disconnecting from the MQTT broker")
    client.disconnect()
