#!/usr/bin/env python3
"""
MQTT Configuration Module

Centralized configuration for MQTT clients.
"""

from dataclasses import dataclass
from typing import List, Optional
import os


@dataclass
class MQTTConfig:
    """Configuration for MQTT connection."""
    host: str = "localhost"
    port: int = 1883
    keepalive: int = 60
    username: Optional[str] = None
    password: Optional[str] = None

    @classmethod
    def from_env(cls) -> 'MQTTConfig':
        """Create configuration from environment variables."""
        return cls(
            host=os.getenv('MQTT_CONNECTION_URL', '**************'),
            port=int(os.getenv('MQTT_CONNECTION_PORT', '1883')),
            keepalive=int(os.getenv('MQTT_CONNECTION_KEEPALIVE', '60')),
            username=os.getenv('MQTT_USERNAME'),
            password=os.getenv('MQTT_PASSWORD')
        )


@dataclass
class TopicConfig:
    """Configuration for MQTT topics."""
    pump_logs: str = "pump_logs"
    qos: int = 0
