#!/usr/bin/env python3
"""
MQTT Configuration Module

Centralized configuration for MQTT clients.
"""

from dataclasses import dataclass
from typing import List, Optional
import os


@dataclass
class MQTTConfig:
    """Configuration for MQTT connection."""
    host: str = "localhost"
    port: int = 1883
    keepalive: int = 60
    username: Optional[str] = None
    password: Optional[str] = None
    clean_session: bool = True
    protocol_version: int = 4  # MQTTv3.1.1

    @classmethod
    def from_env(cls) -> 'MQTTConfig':
        """Create configuration from environment variables."""
        return cls(
            host=os.getenv('MQTT_CONNECTION_URL',
                           'api.smarteye.smartflowtech.org'),
            port=int(os.getenv('MQTT_CONNECTION_PORT', '1883')),
            keepalive=int(os.getenv('MQTT_CONNECTION_KEEPALIVE', '60')),
            username=os.getenv('MQTT_USERNAME'),
            password=os.getenv('MQTT_PASSWORD'),
            clean_session=os.getenv(
                'MQTT_CLEAN_SESSION', 'true').lower() == 'true',
            protocol_version=int(os.getenv('MQTT_PROTOCOL_VERSION', '4'))
        )


@dataclass
class TopicConfig:
    """Configuration for MQTT topics."""
    # Base topics
    pump_logs: str = "pump_logs"
    price_changes: str = "price_changes"
    device_status: str = "device_status"
    remote_config: str = "remote_config"

    # Topic patterns (use {device_id}, {mac}, or {site_id} as placeholders)
    price_change_device: str = "price_change/{mac}"
    device_config: str = "config/{device_id}"
    device_logs: str = "logs/{device_id}"
    site_notifications: str = "notifications/{site_id}"

    # Test topics
    test_topic: str = "test/smarteye"

    # QoS levels
    qos: int = 0
    qos_important: int = 1  # For critical messages like price changes
    qos_critical: int = 2   # For essential system messages

    def get_device_topic(self, pattern: str, device_id: str) -> str:
        """Get a device-specific topic by replacing placeholder."""
        return pattern.format(device_id=device_id)

    def get_mac_topic(self, pattern: str, mac: str) -> str:
        """Get a MAC address-specific topic by replacing placeholder."""
        return pattern.format(mac=mac)

    def get_site_topic(self, pattern: str, site_id: str) -> str:
        """Get a site-specific topic by replacing placeholder."""
        return pattern.format(site_id=site_id)

    def get_price_change_topic(self, mac: str) -> str:
        """Get the price change topic for a specific MAC address."""
        return self.get_mac_topic(self.price_change_device, mac)


@dataclass
class MQTTClientConfig:
    """Configuration for specific MQTT client instances."""
    client_id_prefix: str = "smarteye"
    auto_reconnect: bool = True
    max_reconnect_attempts: int = 5
    reconnect_delay: int = 5  # seconds
    connection_timeout: int = 10  # seconds
    message_retry_count: int = 3

    @classmethod
    def get_default(cls) -> 'MQTTClientConfig':
        """Get default client configuration."""
        return cls()
