#!/usr/bin/env python3
"""
MQTT Price Subscriber

A robust MQTT subscriber for receiving price change notifications.
Includes proper error handling, logging, and graceful shutdown.
"""

import sys
import json
from typing import Dict, Any

import paho.mqtt.client as mqtt

from mqtt_base import BaseMQTT<PERSON>lient
from mqtt_config import MQ<PERSON><PERSON>onfig, TopicConfig


class PriceSubscriber(BaseMQTTClient):
    """MQTT subscriber for price change notifications."""

    def __init__(self, config: MQTTConfig, topic_config: TopicConfig):
        super().__init__(config, client_id="price_subscriber")
        self.topic_config = topic_config
        self.setup_signal_handlers()

    def _setup_additional_callbacks(self) -> None:
        """Setup additional callbacks for the subscriber."""
        if self.client:
            self.client.on_message = self.on_message

    def _on_connect_success(self, client: mqtt.Client) -> None:
        """Called when connection is successful."""
        # Subscribe to price changes topic
        client.subscribe(self.topic_config.price_changes,
                         self.topic_config.qos)
        self.logger.info(
            f"Subscribed to topic: {self.topic_config.price_changes}")

    def on_message(self, client: mqtt.Client, userdata, msg: mqtt.MQTTMessage) -> None:
        """Handle incoming MQTT messages."""
        # Parameters required by paho-mqtt callback signature
        del client, userdata  # Explicitly delete to avoid unused variable warnings

        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            self.logger.info(f"Received message on topic '{topic}': {payload}")

            # Process the price change message
            self._process_price_message(topic, payload)

        except UnicodeDecodeError:
            self.logger.error(
                f"Failed to decode message payload from topic '{msg.topic}'")
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")

    def _process_price_message(self, topic: str, payload: str) -> None:
        """Process the received price change message."""
        try:
            # Try to parse as JSON first
            try:
                data = json.loads(payload)
                if isinstance(data, dict):
                    self._handle_structured_price_data(topic, data)
                else:
                    self._handle_simple_price_message(topic, payload)
            except json.JSONDecodeError:
                # Not JSON, treat as simple text message
                self._handle_simple_price_message(topic, payload)

        except Exception as e:
            self.logger.error(f"Error processing price message: {e}")

    def _handle_structured_price_data(self, topic: str, data: Dict[str, Any]) -> None:
        """Handle structured price data (JSON format)."""
        price = data.get('price', 'Unknown')
        currency = data.get('currency', 'USD')
        timestamp = data.get('timestamp', 'Unknown')

        print(f"💰 Price Update from {topic}:")
        print(f"   Price: {price} {currency}")
        print(f"   Time: {timestamp}")

        # Add your business logic here
        # For example: store in database, trigger alerts, etc.

    def _handle_simple_price_message(self, topic: str, payload: str) -> None:
        """Handle simple text price messages."""
        print(f"💰 Price Update from {topic}: {payload}")

        # Add your business logic here
        # For example: parse price from text, store in database, etc.

    def start_listening(self) -> None:
        """Start listening for messages with automatic reconnection."""
        if not self.client:
            self.logger.error("Client not initialized. Call connect() first.")
            return

        max_retries = 5
        retry_count = 0

        while retry_count < max_retries:
            try:
                self.logger.info(
                    "Starting MQTT listener... Press CTRL+C to exit")
                self.client.loop_forever()
                break  # If we get here, loop_forever ended normally

            except KeyboardInterrupt:
                self.logger.info("Received keyboard interrupt")
                break

            except Exception as e:
                retry_count += 1
                self.logger.error(
                    f"Error in MQTT loop (attempt {retry_count}/{max_retries}): {e}")

                if retry_count < max_retries:
                    self.logger.info(
                        f"Attempting to reconnect in 5 seconds...")
                    import time
                    time.sleep(5)

                    # Try to reconnect
                    self.disconnect()
                    if not self.connect():
                        self.logger.error("Failed to reconnect, will retry...")
                        continue
                else:
                    self.logger.error("Max retries reached, giving up")
                    break

        self.disconnect()


def main():
    """Main entry point for the price subscriber."""
    config = MQTTConfig.from_env()  # Load from environment variables
    topic_config = TopicConfig()
    subscriber = PriceSubscriber(config, topic_config)

    if subscriber.connect():
        subscriber.start_listening()
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
