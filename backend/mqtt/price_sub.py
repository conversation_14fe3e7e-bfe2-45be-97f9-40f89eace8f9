#!/usr/bin/env python3
"""
MQTT Price Subscriber

A robust MQTT subscriber for receiving price change notifications.
Includes proper error handling, logging, and graceful shutdown.
"""

import sys
import json
import logging
from typing import Dict, Any, Optional, Callable, List
from abc import abstractmethod

import paho.mqtt.client as mqtt

from .mqtt_base import BaseMQTTClient
from .mqtt_config import MQTTConfig, TopicConfig, MQTTClientConfig
from .price_publisher import PriceChangeData


class MessageHandler:
    """Base class for handling different types of MQTT messages."""

    @abstractmethod
    def handle_message(self, topic: str, payload: str) -> None:
        """Handle a received message."""
        pass


class PriceChangeHandler(MessageHandler):
    """Handler for price change messages."""

    def __init__(self, callback: Optional[Callable[[PriceChangeData], None]] = None):
        self.callback = callback

    def handle_message(self, topic: str, payload: str) -> None:
        """Handle price change messages."""
        try:
            data = json.loads(payload)
            if isinstance(data, dict):
                self._handle_structured_price_data(topic, data)
            else:
                self._handle_simple_price_message(topic, payload)
        except json.JSONDecodeError:
            self._handle_simple_price_message(topic, payload)

    def _handle_structured_price_data(self, topic: str, data: Dict[str, Any]) -> None:
        """Handle structured price data (JSON format)."""
        try:
            old_price_val = data.get('old_price')
            price_data = PriceChangeData(
                site_id=data.get('site_id', 'unknown'),
                device_id=data.get('device_id'),
                device_mac=data.get('device_mac'),
                new_price=float(data.get('new_price', 0.0)),
                old_price=float(
                    old_price_val) if old_price_val is not None else None,
                product_name=data.get('product_name'),
                currency=data.get('currency', 'NGN')
            )

            print(f"💰 Price Update from {topic}:")
            print(f"   Site: {price_data.site_id}")
            print(f"   Product: {price_data.product_name or 'Unknown'}")
            print(
                f"   New Price: {price_data.new_price} {price_data.currency}")
            if price_data.old_price is not None:
                print(
                    f"   Old Price: {price_data.old_price} {price_data.currency}")
            print(
                f"   Device: {price_data.device_id or price_data.device_mac or 'Unknown'}")

            if self.callback:
                self.callback(price_data)

        except (ValueError, KeyError) as e:
            print(f"❌ Error parsing price data: {e}")

    def _handle_simple_price_message(self, topic: str, payload: str) -> None:
        """Handle simple text price messages."""
        print(f"💰 Price Update from {topic}: {payload}")


class PriceSubscriber(BaseMQTTClient):
    """MQTT subscriber for price change notifications."""

    def __init__(self,
                 config: Optional[MQTTConfig] = None,
                 topic_config: Optional[TopicConfig] = None,
                 client_config: Optional[MQTTClientConfig] = None):
        # Use default configurations if not provided
        mqtt_config = config or MQTTConfig.from_env()
        client_cfg = client_config or MQTTClientConfig.get_default()

        super().__init__(mqtt_config, client_cfg, client_id="price_subscriber")
        self.topic_config = topic_config or TopicConfig()
        self.message_handlers: Dict[str, MessageHandler] = {}
        self.subscribed_topics: List[str] = []
        self.setup_signal_handlers()

    def _setup_additional_callbacks(self) -> None:
        """Setup additional callbacks for the subscriber."""
        if self.client:
            self.client.on_message = self.on_message

    def _on_connect_success(self, client: mqtt.Client) -> None:
        """Called when connection is successful."""
        del client  # Explicitly delete to avoid unused variable warning

        # Subscribe to default price changes topic
        self.subscribe_to_price_changes()

        # Re-subscribe to any previously subscribed topics
        for topic in self.subscribed_topics:
            self.subscribe(topic, self.topic_config.qos)

    def add_message_handler(self, topic_pattern: str, handler: MessageHandler) -> None:
        """Add a message handler for a specific topic pattern."""
        self.message_handlers[topic_pattern] = handler

    def subscribe_to_price_changes(self, qos: Optional[int] = None) -> bool:
        """Subscribe to the main price changes topic."""
        qos_level = qos or self.topic_config.qos_important
        topic = self.topic_config.price_changes

        if self.subscribe(topic, qos_level):
            if topic not in self.subscribed_topics:
                self.subscribed_topics.append(topic)

            # Add default price change handler if none exists
            if topic not in self.message_handlers:
                self.message_handlers[topic] = PriceChangeHandler()

            return True
        return False

    def subscribe_to_device_updates(self, device_id: str, qos: Optional[int] = None) -> bool:
        """Subscribe to price updates for a specific device."""
        qos_level = qos or self.topic_config.qos_important
        topic = self.topic_config.get_device_topic(
            self.topic_config.price_change_device, device_id)

        if self.subscribe(topic, qos_level):
            if topic not in self.subscribed_topics:
                self.subscribed_topics.append(topic)

            # Add price change handler for this device
            self.message_handlers[topic] = PriceChangeHandler()
            return True
        return False

    def subscribe_to_site_notifications(self, site_id: str, qos: Optional[int] = None) -> bool:
        """Subscribe to notifications for a specific site."""
        qos_level = qos or self.topic_config.qos_important
        topic = self.topic_config.get_site_topic(
            self.topic_config.site_notifications, site_id)

        if self.subscribe(topic, qos_level):
            if topic not in self.subscribed_topics:
                self.subscribed_topics.append(topic)

            # Add price change handler for this site
            self.message_handlers[topic] = PriceChangeHandler()
            return True
        return False

    def on_message(self, client: mqtt.Client, userdata, msg: mqtt.MQTTMessage) -> None:
        """Handle incoming MQTT messages."""
        # Parameters required by paho-mqtt callback signature
        del client, userdata  # Explicitly delete to avoid unused variable warnings

        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            self._log_with_context(
                logging.INFO, f"Received message on topic '{topic}': {payload[:100]}...")

            # Find appropriate handler for this topic
            handler = self._find_message_handler(topic)
            if handler:
                handler.handle_message(topic, payload)
            else:
                self._log_with_context(
                    logging.WARNING, f"No handler found for topic '{topic}'")

        except UnicodeDecodeError:
            self._log_with_context(
                logging.ERROR, f"Failed to decode message payload from topic '{msg.topic}'")
        except Exception as e:
            self._log_with_context(
                logging.ERROR, f"Error processing message: {e}")

    def _find_message_handler(self, topic: str) -> Optional[MessageHandler]:
        """Find the appropriate message handler for a topic."""
        # First try exact match
        if topic in self.message_handlers:
            return self.message_handlers[topic]

        # Then try pattern matching (simple wildcard support)
        for pattern, handler in self.message_handlers.items():
            if self._topic_matches_pattern(topic, pattern):
                return handler

        return None

    def _topic_matches_pattern(self, topic: str, pattern: str) -> bool:
        """Check if a topic matches a pattern (simple wildcard support)."""
        # Simple wildcard matching - can be enhanced for more complex patterns
        if '+' in pattern or '#' in pattern:
            # Convert MQTT wildcards to regex-like patterns
            regex_pattern = pattern.replace('+', '[^/]+').replace('#', '.*')
            import re
            return bool(re.match(f"^{regex_pattern}$", topic))

        return topic == pattern

    def start_listening(self) -> None:
        """Start listening for messages with automatic reconnection."""
        if not self.client:
            self._log_with_context(
                logging.ERROR, "Client not initialized. Call connect() first.")
            return

        max_retries = self.client_config.max_reconnect_attempts
        retry_count = 0

        while retry_count < max_retries:
            try:
                self._log_with_context(
                    logging.INFO, "Starting MQTT listener... Press CTRL+C to exit")
                self.client.loop_forever()
                break  # If we get here, loop_forever ended normally

            except KeyboardInterrupt:
                self._log_with_context(
                    logging.INFO, "Received keyboard interrupt")
                break

            except Exception as e:
                retry_count += 1
                self._log_with_context(
                    logging.ERROR, f"Error in MQTT loop (attempt {retry_count}/{max_retries}): {e}")

                if retry_count < max_retries:
                    self._log_with_context(
                        logging.INFO, f"Attempting to reconnect in {self.client_config.reconnect_delay} seconds...")
                    import time
                    time.sleep(self.client_config.reconnect_delay)

                    # Try to reconnect
                    self.disconnect()
                    if not self.connect():
                        self._log_with_context(
                            logging.ERROR, "Failed to reconnect, will retry...")
                        continue
                else:
                    self._log_with_context(
                        logging.ERROR, "Max retries reached, giving up")
                    break

        self.disconnect()

    @classmethod
    def create_default(cls) -> 'PriceSubscriber':
        """Create a PriceSubscriber with default configuration."""
        return cls()

    @classmethod
    def create_with_config(cls, mqtt_config: MQTTConfig, topic_config: TopicConfig) -> 'PriceSubscriber':
        """Create a PriceSubscriber with custom configuration."""
        return cls(mqtt_config, topic_config)


def main():
    """Main entry point for the price subscriber."""
    subscriber = PriceSubscriber.create_default()

    # Add custom message handler with callback
    def price_change_callback(price_data: PriceChangeData):
        print(
            f"🔔 Custom callback: Price changed to {price_data.new_price} {price_data.currency}")
        # Add your custom business logic here
        # e.g., save to database, send notifications, etc.

    # Add custom handler for price changes
    custom_handler = PriceChangeHandler(callback=price_change_callback)
    subscriber.add_message_handler(
        subscriber.topic_config.price_changes, custom_handler)

    # Subscribe to additional topics if needed
    # subscriber.subscribe_to_device_updates("device123")
    # subscriber.subscribe_to_site_notifications("site456")

    if subscriber.connect():
        print(f"✅ Connected to MQTT broker")
        print(f"📡 Subscribed topics: {subscriber.subscribed_topics}")
        subscriber.start_listening()
    else:
        print("❌ Failed to connect to MQTT broker")
        sys.exit(1)


if __name__ == "__main__":
    main()
