
#!/usr/bin/env python3
"""
MQTT Utilities

Utility functions for MQTT operations in the SmartEye system.
This module provides helper functions for MQTT-related operations.
"""

import json
import logging
from datetime import datetime
from backend import models, utils
from .mqtt_service import get_mqtt_service
from .price_publisher import PriceChangeData


def get_site_price_changes_configs(site_id: str) -> bool:
    """
    Get price change configurations for all devices in a site and publish via MQTT.

    Args:
        site_id: The site ID to get configurations for

    Returns:
        True if successful, False otherwise
    """
    logger = logging.getLogger("mqtt.utils")

    try:
        device_in_pump = [
            each.Device.Device_unique_address for each in models.Pump.objects.filter(Site_id=site_id) if each.Device]
        if not device_in_pump:
            logger.warning(f"No devices found for site {site_id}")
            return False

        data_final = []
        # download remote config for devices
        for each in device_in_pump:
            try:
                data_set = {}
                config = utils.make_request(each)
                if not config or 'data' not in config:
                    logger.warning(
                        f"Invalid config response for device {each}")
                    continue
                data_set[each] = config['data']
                data_final.append(data_set)
            except Exception as e:
                logger.error(
                    f"Error getting config for device {each}: {str(e)}")
                continue

        if not data_final:
            logger.warning("No valid configurations found")
            return False

        # Use the new MQTT service to publish configurations
        mqtt_service = get_mqtt_service()
        price_data_list = []

        # Process remote config and create price data objects
        for remote_config in data_final:
            try:
                for device_mac, records in remote_config.items():
                    for record in records:
                        try:
                            # Parse price time
                            try:
                                dt = datetime.strptime(record['Price_time'].split('.')[
                                                       0], "%Y-%m-%dT%H:%M:%S")
                                price_time = dt
                            except Exception:
                                price_time = datetime.now()

                            # Create price data object
                            price_data = PriceChangeData(
                                site_id=str(site_id),
                                device_mac=device_mac,
                                new_price=float(record.get('Price', 0.0)),
                                currency="NGN",
                                scheduled_time=price_time
                            )
                            price_data_list.append(price_data)

                        except Exception as e:
                            logger.error(f"Error processing record: {e}")
                            continue

            except Exception as e:
                logger.error(f"Error processing remote config: {str(e)}")
                continue

        # Publish all price updates for the site
        if price_data_list:
            results = mqtt_service.publish_site_price_updates(
                price_data_list, str(site_id))
            success_count = sum(1 for success in results.values() if success)
            logger.info(
                f"Published {success_count}/{len(results)} price updates for site {site_id}")
            return success_count > 0

        return True

    except Exception as e:
        logger.error(f"Error in get_site_price_changes_configs: {str(e)}")
        return False


def publish_device_price_change(device_id: str, price_data: PriceChangeData) -> bool:
    """
    Publish a price change for a specific device.

    Args:
        device_id: Device ID or MAC address
        price_data: Price change data

    Returns:
        True if successful, False otherwise
    """
    try:
        mqtt_service = get_mqtt_service()
        publisher = mqtt_service.get_publisher()
        if publisher:
            return publisher.publish_device_price_update(price_data, device_id)
        else:
            logging.warning("MQTT publisher not available")
            return False
    except Exception as e:
        logging.error(f"Error publishing device price change: {e}")
        return False


def send_remote_config_via_mqtt(site_id: str) -> bool:
    """
    Send remote configuration to individual IoT devices via MQTT price_change topics.
    This sends the same config data that send_remote_config() sends, but via MQTT.

    Args:
        site_id: The site ID to send configurations for

    Returns:
        True if successful, False otherwise
    """
    logger = logging.getLogger("mqtt.utils")

    try:
        # Get all devices for the site (same logic as send_remote_config)
        device_in_pump = [
            each.Device.Device_unique_address for each in models.Pump.objects.filter(Site_id=site_id) if each.Device]

        if not device_in_pump:
            logger.warning(f"No devices found for site {site_id}")
            return False

        mqtt_service = get_mqtt_service()
        publisher = mqtt_service.get_publisher()

        if not publisher:
            logger.error("MQTT publisher not available")
            return False

        success_count = 0
        total_devices = len(device_in_pump)

        # Process each device (same logic as send_remote_config)
        for device_mac in device_in_pump:
            try:
                # Get config for this device (same as send_remote_config)
                config = utils.make_request(device_mac)
                if not config or 'data' not in config:
                    logger.warning(
                        f"Invalid config response for device {device_mac}")
                    continue

                # Process the config data (same formatting as send_remote_config)
                config_data = config['data']
                for record in config_data:
                    try:
                        # Format price time (same logic as send_remote_config)
                        if 'Price_time' in record:
                            try:
                                dt = datetime.strptime(record['Price_time'].split('.')[
                                                       0], "%Y-%m-%dT%H:%M:%S")
                                record['Price_time'] = dt.strftime(
                                    "%Y-%m-%dT%H:%M:%S")
                            except Exception:
                                record['Price_time'] = datetime.now().strftime(
                                    "%Y-%m-%dT%H:%M:%S")
                    except Exception as e:
                        logger.warning(
                            f"Error formatting record for device {device_mac}: {e}")
                        continue

                # Create the config message in the same format as send_remote_config
                remote_config = {device_mac: config_data}
                config_message = {
                    "type": "remote_config",
                    "timestamp": datetime.now().isoformat(),
                    "site_id": str(site_id),
                    "device_mac": device_mac,
                    "config": remote_config
                }

                # Send via MQTT to price_change/{mac} topic
                topic = f"price_change/{device_mac}"
                message_json = json.dumps(config_message, indent=2)

                if publisher.publish(topic, message_json, publisher.topic_config.qos_important):
                    success_count += 1
                    logger.info(
                        f"✅ Sent config to device {device_mac} via MQTT")
                else:
                    logger.error(
                        f"❌ Failed to send config to device {device_mac} via MQTT")

            except Exception as e:
                logger.error(
                    f"Error processing config for device {device_mac}: {e}")
                continue

        logger.info(
            f"📡 MQTT config sent to {success_count}/{total_devices} devices for site {site_id}")
        return success_count > 0

    except Exception as e:
        logger.error(f"Error in send_remote_config_via_mqtt: {e}")
        return False
