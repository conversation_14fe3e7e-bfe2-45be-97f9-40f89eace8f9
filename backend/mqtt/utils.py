

import json
from datetime import datetime
from backend import models, utils
from backend.devices.utils import get_device_mac_address


def get_site_price_changes_configs(site_id):
    try:
        device_in_pump = [
            each.Device.Device_unique_address for each in models.Pump.objects.filter(Site_id=site_id)]
        if not device_in_pump:
            print("No devices found for site")
            return False

        data_final = []
        # download remote config for devices
        for each in device_in_pump:
            try:
                data_set = {}
                config = utils.make_request(each)
                if not config or 'data' not in config:
                    print(f"Invalid config response for device {each}")
                    continue
                data_set[each] = config['data']
                data_final.append(data_set)
            except Exception as e:
                print(f"Error getting config for device {each}: {str(e)}")
                continue

        if not data_final:
            print("No valid configurations found")
            return False
        # send downloaded remote config to remote config service
        for remote_config in data_final:
            try:
                for key, records in remote_config.items():
                    for record in records:
                        try:
                            dt = datetime.strptime(record['Price_time'].split('.')[
                                                   0], "%Y-%m-%dT%H:%M:%S")
                            record['Price_time'] = dt.strftime(
                                "%Y-%m-%dT%H:%M:%S")
                        except Exception as e:
                            # Use server time as fallback
                            record['Price_time'] = datetime.now().strftime(
                                "%Y-%m-%dT%H:%M:%S")

                json_object = json.dumps(remote_config)
                device_mac_address = get_device_mac_address(
                    remote_config.Device_id)
                topic = f"price_change/{device_mac_address}"
                response = publish_price_change(topic, json_object)
                if not response:
                    print("Empty response from remote config service")
                    continue
            except Exception as e:
                print(f"Error processing remote config: {str(e)}")
                continue

        return False

    except Exception as e:
        print(f"Error in send_remote_config: {str(e)}")
        return False
