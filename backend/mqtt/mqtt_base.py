#!/usr/bin/env python3
"""
Base MQTT Client Module

Provides a base class for MQTT clients with common functionality.
"""

import logging
import signal
import sys
from abc import ABC, abstractmethod
from typing import Optional

import paho.mqtt.client as mqtt  # type: ignore

from mqtt_config import MQTTConfig


class BaseMQTTClient(ABC):
    """Base class for MQTT clients."""

    def __init__(self, config: MQTTConfig, client_id: Optional[str] = None):
        self.config = config
        self.client: Optional[mqtt.Client] = None
        self.is_connected = False
        self.client_id = client_id
        self._setup_logging()

    def _setup_logging(self) -> None:
        """Configure logging for the application."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(self.__class__.__name__)

    def setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum: int, frame) -> None:
        """Handle shutdown signals gracefully."""
        # frame parameter is required by signal handler signature but not used
        del frame  # Explicitly delete to avoid unused variable warning
        self.logger.info(
            f"Received signal {signum}, shutting down gracefully...")
        self.disconnect()
        sys.exit(0)

    def on_connect(self, client: mqtt.Client, userdata, flags, rc: int) -> None:
        """Callback for when the client connects to the broker."""
        # userdata and flags parameters are required by paho-mqtt but not always used
        del userdata, flags  # Explicitly delete to avoid unused variable warnings

        if rc == 0:
            self.is_connected = True
            self.logger.info(
                f"Connected to MQTT broker at {self.config.host}:{self.config.port}")
            self._on_connect_success(client)
        else:
            self.logger.error(
                f"Failed to connect to MQTT broker. Return code: {rc}")
            self.is_connected = False

    def on_disconnect(self, client: mqtt.Client, userdata, rc: int) -> None:
        """Callback for when the client disconnects from the broker."""
        # client and userdata parameters are required by paho-mqtt but not always used
        del client, userdata  # Explicitly delete to avoid unused variable warnings

        self.is_connected = False
        if rc != 0:
            self.logger.warning(
                f"Unexpected disconnection from MQTT broker (code: {rc})")
        else:
            self.logger.info("Disconnected from MQTT broker")

    def _on_log(self, client: mqtt.Client, userdata, level: int, buf: str) -> None:
        """Callback for MQTT client logging."""
        # client and userdata parameters are required by paho-mqtt but not always used
        del client, userdata  # Explicitly delete to avoid unused variable warnings

        # Only log important messages to avoid spam
        if level <= mqtt.MQTT_LOG_WARNING:
            if "struct.error" in buf or "bad char in struct format" in buf:
                self.logger.error(f"MQTT protocol error detected: {buf}")
                self.logger.info(
                    "Attempting to reconnect due to protocol error...")
                # Don't auto-reconnect here to avoid loops, let the application handle it
            else:
                self.logger.debug(f"MQTT log (level {level}): {buf}")

    @abstractmethod
    def _on_connect_success(self, client: mqtt.Client) -> None:
        """Called when connection is successful. Override in subclasses."""
        pass

    def connect(self) -> bool:
        """Connect to the MQTT broker."""
        try:
            # Use MQTT protocol version 3.1.1 for better compatibility
            self.client = mqtt.Client(
                client_id=self.client_id,
                protocol=mqtt.MQTTv311,
                clean_session=True
            )

            # Set up authentication if provided
            if self.config.username and self.config.password:
                self.client.username_pw_set(  # type: ignore
                    self.config.username, self.config.password)

            # Set up callbacks
            self.client.on_connect = self.on_connect  # type: ignore
            self.client.on_disconnect = self.on_disconnect  # type: ignore
            self.client.on_log = self._on_log  # type: ignore
            self._setup_additional_callbacks()

            self.logger.info(
                f"Connecting to MQTT broker at {self.config.host}:{self.config.port}")

            # Use connect_async for better error handling
            result = self.client.connect(  # type: ignore
                self.config.host,
                self.config.port,
                self.config.keepalive
            )

            if result != 0:
                self.logger.error(
                    f"Failed to connect to MQTT broker. Return code: {result}")
                return False

            # Start the network loop to process callbacks
            self.client.loop_start()  # type: ignore

            # Wait for connection to be established
            import time
            timeout = 10  # Increased timeout to 10 seconds
            start_time = time.time()
            while not self.is_connected and (time.time() - start_time) < timeout:
                time.sleep(0.1)

            if not self.is_connected:
                self.logger.error(
                    "Connection timeout - failed to connect within 10 seconds")
                self.client.loop_stop()  # type: ignore
                return False

            return True

        except Exception as e:
            self.logger.error(
                f"Exception while connecting to MQTT broker: {e}")
            if self.client:
                try:
                    self.client.loop_stop()
                except:
                    pass
            return False

    def _setup_additional_callbacks(self) -> None:
        """Setup additional callbacks. Override in subclasses if needed."""
        pass

    def disconnect(self) -> None:
        """Disconnect from the MQTT broker."""
        if self.client:
            if self.is_connected:
                self.logger.info("Disconnecting from MQTT broker...")
                self.client.disconnect()
            self.client.loop_stop()

    def publish(self, topic: str, payload: str, qos: int = 0, retain: bool = False) -> bool:
        """Publish a message to the specified topic."""
        if not self.client or not self.is_connected:
            self.logger.error("Client not connected. Cannot publish message.")
            return False

        try:
            result = self.client.publish(topic, payload, qos, retain)
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(
                    f"Published message to topic '{topic}': {payload}")
                return True
            else:
                self.logger.error(
                    f"Failed to publish message. Return code: {result.rc}")
                return False
        except Exception as e:
            self.logger.error(f"Exception while publishing message: {e}")
            return False
