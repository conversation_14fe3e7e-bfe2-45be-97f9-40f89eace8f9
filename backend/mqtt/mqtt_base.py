#!/usr/bin/env python3
"""
Base MQTT Client Module

Provides a base class for MQTT clients with common functionality.
"""

import logging
import signal
import sys
import time
import uuid
from abc import ABC, abstractmethod
from typing import Optional, Callable, Dict, Any
from enum import Enum

import paho.mqtt.client as mqtt  # type: ignore

from .mqtt_config import MQTTConfig, MQTTClientConfig
from .logging_config import get_mqtt_logger, MQTTLogContext, log_connection_event, log_publish_event, log_subscribe_event


class ConnectionState(Enum):
    """MQTT connection states."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    FAILED = "failed"


class BaseMQTTClient(ABC):
    """Base class for MQTT clients with enhanced error handling and reconnection."""

    def __init__(self,
                 config: MQTTConfig,
                 client_config: Optional[MQTTClientConfig] = None,
                 client_id: Optional[str] = None):
        self.config = config
        self.client_config = client_config or MQTTClientConfig.get_default()
        self.client: Optional[mqtt.Client] = None
        self.connection_state = ConnectionState.DISCONNECTED
        self.reconnect_attempts = 0
        self.last_error: Optional[str] = None

        # Generate unique client ID if not provided
        if client_id:
            self.client_id = client_id
        else:
            unique_id = str(uuid.uuid4())[:8]
            self.client_id = f"{self.client_config.client_id_prefix}_{unique_id}"

        self._setup_logging()
        self._message_callbacks: Dict[str, Callable] = {}
        self._is_shutting_down = False

    def _setup_logging(self) -> None:
        """Configure logging for the application."""
        self.logger = get_mqtt_logger(self.__class__.__name__.lower())

    def _log_with_context(self, level: int, message: str, **kwargs) -> None:
        """Log message with client context."""
        extra = {'client_id': self.client_id}
        extra.update(kwargs)
        self.logger.log(level, message, extra=extra)

    def setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum: int, frame) -> None:
        """Handle shutdown signals gracefully."""
        # frame parameter is required by signal handler signature but not used
        del frame  # Explicitly delete to avoid unused variable warning
        self._is_shutting_down = True
        self._log_with_context(
            logging.INFO, f"Received signal {signum}, shutting down gracefully...")
        self.disconnect()
        sys.exit(0)

    def on_connect(self, client: mqtt.Client, userdata, flags, rc: int) -> None:
        """Callback for when the client connects to the broker."""
        # userdata and flags parameters are required by paho-mqtt but not always used
        del userdata, flags  # Explicitly delete to avoid unused variable warnings

        if rc == 0:
            self.connection_state = ConnectionState.CONNECTED
            self.reconnect_attempts = 0
            self.last_error = None
            log_connection_event(
                self.logger, self.client_id, f"{self.config.host}:{self.config.port}", True)
            self._on_connect_success(client)
        else:
            self.connection_state = ConnectionState.FAILED
            error_msg = self._get_connection_error_message(rc)
            self.last_error = error_msg
            log_connection_event(
                self.logger, self.client_id, f"{self.config.host}:{self.config.port}", False, error_msg)

    def _get_connection_error_message(self, rc: int) -> str:
        """Get human-readable error message for connection return code."""
        error_messages = {
            1: "Connection refused - incorrect protocol version",
            2: "Connection refused - invalid client identifier",
            3: "Connection refused - server unavailable",
            4: "Connection refused - bad username or password",
            5: "Connection refused - not authorised"
        }
        return error_messages.get(rc, f"Unknown connection error (code: {rc})")

    def on_disconnect(self, client: mqtt.Client, userdata, rc: int) -> None:
        """Callback for when the client disconnects from the broker."""
        # client and userdata parameters are required by paho-mqtt but not always used
        del client, userdata  # Explicitly delete to avoid unused variable warnings

        if rc != 0:
            self.connection_state = ConnectionState.DISCONNECTED
            self._log_with_context(
                logging.WARNING, f"Unexpected disconnection from MQTT broker (code: {rc})")

            # Attempt reconnection if enabled and not shutting down
            if (self.client_config.auto_reconnect and
                not self._is_shutting_down and
                    self.reconnect_attempts < self.client_config.max_reconnect_attempts):
                self._attempt_reconnect()
        else:
            self.connection_state = ConnectionState.DISCONNECTED
            self._log_with_context(
                logging.INFO, "Disconnected from MQTT broker")

    def _attempt_reconnect(self) -> None:
        """Attempt to reconnect to the MQTT broker."""
        self.reconnect_attempts += 1
        self.connection_state = ConnectionState.RECONNECTING

        self._log_with_context(
            logging.INFO,
            f"Attempting reconnection {self.reconnect_attempts}/{self.client_config.max_reconnect_attempts} "
            f"in {self.client_config.reconnect_delay} seconds..."
        )

        time.sleep(self.client_config.reconnect_delay)

        try:
            if self.client:
                self.client.reconnect()
        except Exception as e:
            self._log_with_context(
                logging.ERROR, f"Reconnection attempt failed: {e}")
            if self.reconnect_attempts >= self.client_config.max_reconnect_attempts:
                self.connection_state = ConnectionState.FAILED
                self._log_with_context(
                    logging.ERROR, "Max reconnection attempts reached. Giving up.")

    def _on_log(self, client: mqtt.Client, userdata, level: int, buf: str) -> None:
        """Callback for MQTT client logging."""
        # client and userdata parameters are required by paho-mqtt but not always used
        del client, userdata  # Explicitly delete to avoid unused variable warnings

        # Only log important messages to avoid spam
        if level <= mqtt.MQTT_LOG_WARNING:
            if "struct.error" in buf or "bad char in struct format" in buf:
                self._log_with_context(
                    logging.ERROR, f"MQTT protocol error detected: {buf}")
                self.last_error = f"Protocol error: {buf}"
                # Don't auto-reconnect here to avoid loops, let the application handle it
            else:
                self._log_with_context(
                    logging.DEBUG, f"MQTT log (level {level}): {buf}")

    @abstractmethod
    def _on_connect_success(self, client: mqtt.Client) -> None:
        """Called when connection is successful. Override in subclasses."""
        pass

    def connect(self) -> bool:
        """Connect to the MQTT broker."""
        try:
            self.connection_state = ConnectionState.CONNECTING

            # Use MQTT protocol version based on config
            protocol_map = {3: mqtt.MQTTv31, 4: mqtt.MQTTv311, 5: mqtt.MQTTv5}
            protocol = protocol_map.get(
                self.config.protocol_version, mqtt.MQTTv311)

            self.client = mqtt.Client(
                client_id=self.client_id,
                protocol=protocol,
                clean_session=self.config.clean_session
            )

            # Set up authentication if provided
            if self.config.username and self.config.password:
                self.client.username_pw_set(  # type: ignore
                    self.config.username, self.config.password)

            # Set up callbacks
            self.client.on_connect = self.on_connect  # type: ignore
            self.client.on_disconnect = self.on_disconnect  # type: ignore
            self.client.on_log = self._on_log  # type: ignore
            self._setup_additional_callbacks()

            self._log_with_context(
                logging.INFO, f"Connecting to MQTT broker at {self.config.host}:{self.config.port}")

            # Use connect for synchronous connection
            result = self.client.connect(  # type: ignore
                self.config.host,
                self.config.port,
                self.config.keepalive
            )

            if result != 0:
                error_msg = self._get_connection_error_message(result)
                self.last_error = error_msg
                self.connection_state = ConnectionState.FAILED
                self._log_with_context(
                    logging.ERROR, f"Failed to connect to MQTT broker: {error_msg}")
                return False

            # Start the network loop to process callbacks
            self.client.loop_start()  # type: ignore

            # Wait for connection to be established
            timeout = self.client_config.connection_timeout
            start_time = time.time()
            while (self.connection_state != ConnectionState.CONNECTED and
                   (time.time() - start_time) < timeout):
                time.sleep(0.1)

            if self.connection_state != ConnectionState.CONNECTED:
                self.connection_state = ConnectionState.FAILED
                self.last_error = "Connection timeout"
                self._log_with_context(
                    logging.ERROR, f"Connection timeout - failed to connect within {timeout} seconds")
                self.client.loop_stop()  # type: ignore
                return False

            return True

        except Exception as e:
            self.connection_state = ConnectionState.FAILED
            self.last_error = str(e)
            self._log_with_context(
                logging.ERROR, f"Exception while connecting to MQTT broker: {e}")
            if self.client:
                try:
                    self.client.loop_stop()
                except:
                    pass
            return False

    def _setup_additional_callbacks(self) -> None:
        """Setup additional callbacks. Override in subclasses if needed."""
        pass

    def disconnect(self) -> None:
        """Disconnect from the MQTT broker."""
        if self.client:
            if self.connection_state == ConnectionState.CONNECTED:
                self._log_with_context(
                    logging.INFO, "Disconnecting from MQTT broker...")
                self.client.disconnect()
            self.client.loop_stop()
            self.connection_state = ConnectionState.DISCONNECTED

    def publish(self, topic: str, payload: str, qos: int = 0, retain: bool = False) -> bool:
        """Publish a message to the specified topic."""
        if not self.client or self.connection_state != ConnectionState.CONNECTED:
            self._log_with_context(
                logging.ERROR, "Client not connected. Cannot publish message.")
            return False

        try:
            result = self.client.publish(topic, payload, qos, retain)
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self._log_with_context(
                    logging.INFO, f"Published message to topic '{topic}': {payload[:100]}...")
                return True
            else:
                self._log_with_context(
                    logging.ERROR, f"Failed to publish message. Return code: {result.rc}")
                return False
        except Exception as e:
            self._log_with_context(
                logging.ERROR, f"Exception while publishing message: {e}")
            return False

    def publish_with_retry(self, topic: str, payload: str, qos: int = 0, retain: bool = False) -> bool:
        """Publish a message with retry logic."""
        for attempt in range(self.client_config.message_retry_count):
            if self.publish(topic, payload, qos, retain):
                return True

            if attempt < self.client_config.message_retry_count - 1:
                self._log_with_context(
                    logging.WARNING,
                    f"Publish attempt {attempt + 1} failed, retrying in 1 second..."
                )
                time.sleep(1)

        self._log_with_context(
            logging.ERROR,
            f"Failed to publish message after {self.client_config.message_retry_count} attempts"
        )
        return False

    def subscribe(self, topic: str, qos: int = 0) -> bool:
        """Subscribe to a topic."""
        if not self.client or self.connection_state != ConnectionState.CONNECTED:
            self._log_with_context(
                logging.ERROR, "Client not connected. Cannot subscribe.")
            return False

        try:
            result, _ = self.client.subscribe(topic, qos)
            if result == mqtt.MQTT_ERR_SUCCESS:
                self._log_with_context(
                    logging.INFO, f"Subscribed to topic '{topic}'")
                return True
            else:
                self._log_with_context(
                    logging.ERROR, f"Failed to subscribe to topic '{topic}'. Return code: {result}")
                return False
        except Exception as e:
            self._log_with_context(
                logging.ERROR, f"Exception while subscribing: {e}")
            return False

    def unsubscribe(self, topic: str) -> bool:
        """Unsubscribe from a topic."""
        if not self.client or self.connection_state != ConnectionState.CONNECTED:
            self._log_with_context(
                logging.ERROR, "Client not connected. Cannot unsubscribe.")
            return False

        try:
            result, _ = self.client.unsubscribe(topic)
            if result == mqtt.MQTT_ERR_SUCCESS:
                self._log_with_context(
                    logging.INFO, f"Unsubscribed from topic '{topic}'")
                return True
            else:
                self._log_with_context(
                    logging.ERROR, f"Failed to unsubscribe from topic '{topic}'. Return code: {result}")
                return False
        except Exception as e:
            self._log_with_context(
                logging.ERROR, f"Exception while unsubscribing: {e}")
            return False

    @property
    def is_connected(self) -> bool:
        """Check if client is connected."""
        return self.connection_state == ConnectionState.CONNECTED

    def get_status(self) -> Dict[str, Any]:
        """Get current client status."""
        return {
            "client_id": self.client_id,
            "connection_state": self.connection_state.value,
            "reconnect_attempts": self.reconnect_attempts,
            "last_error": self.last_error,
            "broker": f"{self.config.host}:{self.config.port}"
        }
