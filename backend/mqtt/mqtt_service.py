#!/usr/bin/env python3
"""
MQTT Service Manager

Centralized service for managing MQTT connections, publishers, and subscribers.
Provides a unified interface for MQTT operations in the SmartEye system.
"""

import logging
import threading
import time
from typing import Dict, Optional, List, Any, Callable
from dataclasses import dataclass
from enum import Enum

from .mqtt_config import MQTTConfig, TopicConfig, MQTTClientConfig
from .price_publisher import PricePublisher, PriceChangeData
from .price_sub import PriceSubscriber, PriceChangeHandler


class ServiceState(Enum):
    """MQTT service states."""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class MQTTServiceConfig:
    """Configuration for MQTT service."""
    auto_start_publisher: bool = True
    auto_start_subscriber: bool = False
    enable_health_check: bool = True
    health_check_interval: int = 30  # seconds
    max_connection_retries: int = 3
    
    @classmethod
    def get_default(cls) -> 'MQTTServiceConfig':
        """Get default service configuration."""
        return cls()


class MQTTService:
    """Centralized MQTT service manager."""
    
    def __init__(self, 
                 mqtt_config: Optional[MQTTConfig] = None,
                 topic_config: Optional[TopicConfig] = None,
                 client_config: Optional[MQTTClientConfig] = None,
                 service_config: Optional[MQTTServiceConfig] = None):
        
        self.mqtt_config = mqtt_config or MQTTConfig.from_env()
        self.topic_config = topic_config or TopicConfig()
        self.client_config = client_config or MQTTClientConfig.get_default()
        self.service_config = service_config or MQTTServiceConfig.get_default()
        
        self.state = ServiceState.STOPPED
        self.publisher: Optional[PricePublisher] = None
        self.subscriber: Optional[PriceSubscriber] = None
        
        self._health_check_thread: Optional[threading.Thread] = None
        self._stop_health_check = threading.Event()
        self._lock = threading.Lock()
        
        self.logger = logging.getLogger("mqtt.service")
        self._setup_logging()
    
    def _setup_logging(self) -> None:
        """Setup logging for the service."""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def start(self) -> bool:
        """Start the MQTT service."""
        with self._lock:
            if self.state != ServiceState.STOPPED:
                self.logger.warning(f"Service already in state: {self.state.value}")
                return False
            
            self.state = ServiceState.STARTING
            self.logger.info("Starting MQTT service...")
            
            try:
                # Initialize publisher if enabled
                if self.service_config.auto_start_publisher:
                    self.publisher = PricePublisher(
                        self.mqtt_config, self.topic_config, self.client_config)
                    
                    if not self.publisher.connect():
                        self.logger.error("Failed to connect publisher")
                        self.state = ServiceState.ERROR
                        return False
                    
                    self.logger.info("✅ Publisher connected")
                
                # Initialize subscriber if enabled
                if self.service_config.auto_start_subscriber:
                    self.subscriber = PriceSubscriber(
                        self.mqtt_config, self.topic_config, self.client_config)
                    
                    if not self.subscriber.connect():
                        self.logger.error("Failed to connect subscriber")
                        self.state = ServiceState.ERROR
                        return False
                    
                    self.logger.info("✅ Subscriber connected")
                
                # Start health check if enabled
                if self.service_config.enable_health_check:
                    self._start_health_check()
                
                self.state = ServiceState.RUNNING
                self.logger.info("🚀 MQTT service started successfully")
                return True
                
            except Exception as e:
                self.logger.error(f"Error starting MQTT service: {e}")
                self.state = ServiceState.ERROR
                return False
    
    def stop(self) -> None:
        """Stop the MQTT service."""
        with self._lock:
            if self.state == ServiceState.STOPPED:
                return
            
            self.state = ServiceState.STOPPING
            self.logger.info("Stopping MQTT service...")
            
            # Stop health check
            if self._health_check_thread:
                self._stop_health_check.set()
                self._health_check_thread.join(timeout=5)
            
            # Disconnect clients
            if self.publisher:
                self.publisher.disconnect()
                self.publisher = None
                self.logger.info("Publisher disconnected")
            
            if self.subscriber:
                self.subscriber.disconnect()
                self.subscriber = None
                self.logger.info("Subscriber disconnected")
            
            self.state = ServiceState.STOPPED
            self.logger.info("🛑 MQTT service stopped")
    
    def get_publisher(self) -> Optional[PricePublisher]:
        """Get the publisher instance."""
        return self.publisher
    
    def get_subscriber(self) -> Optional[PriceSubscriber]:
        """Get the subscriber instance."""
        return self.subscriber
    
    def publish_price_update(self, price_data: PriceChangeData, use_retry: bool = True) -> bool:
        """Publish a price update using the managed publisher."""
        if not self.publisher or not self.publisher.is_connected:
            self.logger.error("Publisher not available or not connected")
            return False
        
        return self.publisher.publish_price_update(price_data, use_retry)
    
    def publish_site_price_updates(self, price_data_list: List[PriceChangeData], site_id: str) -> Dict[str, bool]:
        """Publish price updates for multiple devices in a site."""
        if not self.publisher or not self.publisher.is_connected:
            self.logger.error("Publisher not available or not connected")
            return {}
        
        return self.publisher.publish_site_price_updates(price_data_list, site_id)
    
    def add_price_change_callback(self, callback: Callable[[PriceChangeData], None]) -> bool:
        """Add a callback for price change notifications."""
        if not self.subscriber:
            self.logger.error("Subscriber not available")
            return False
        
        handler = PriceChangeHandler(callback=callback)
        self.subscriber.add_message_handler(self.topic_config.price_changes, handler)
        return True
    
    def subscribe_to_device_updates(self, device_id: str) -> bool:
        """Subscribe to price updates for a specific device."""
        if not self.subscriber or not self.subscriber.is_connected:
            self.logger.error("Subscriber not available or not connected")
            return False
        
        return self.subscriber.subscribe_to_device_updates(device_id)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current service status."""
        status = {
            "service_state": self.state.value,
            "publisher_connected": self.publisher.is_connected if self.publisher else False,
            "subscriber_connected": self.subscriber.is_connected if self.subscriber else False,
            "broker": f"{self.mqtt_config.host}:{self.mqtt_config.port}",
            "subscribed_topics": self.subscriber.subscribed_topics if self.subscriber else []
        }
        
        if self.publisher:
            status["publisher_status"] = self.publisher.get_status()
        
        if self.subscriber:
            status["subscriber_status"] = self.subscriber.get_status()
        
        return status
    
    def _start_health_check(self) -> None:
        """Start the health check thread."""
        self._stop_health_check.clear()
        self._health_check_thread = threading.Thread(
            target=self._health_check_loop, daemon=True)
        self._health_check_thread.start()
        self.logger.info("Health check started")
    
    def _health_check_loop(self) -> None:
        """Health check loop."""
        while not self._stop_health_check.wait(self.service_config.health_check_interval):
            try:
                self._perform_health_check()
            except Exception as e:
                self.logger.error(f"Error in health check: {e}")
    
    def _perform_health_check(self) -> None:
        """Perform health check on MQTT clients."""
        issues = []
        
        if self.publisher and not self.publisher.is_connected:
            issues.append("Publisher disconnected")
        
        if self.subscriber and not self.subscriber.is_connected:
            issues.append("Subscriber disconnected")
        
        if issues:
            self.logger.warning(f"Health check issues: {', '.join(issues)}")
            # Could implement auto-reconnection logic here
        else:
            self.logger.debug("Health check passed")
    
    @classmethod
    def create_default(cls) -> 'MQTTService':
        """Create an MQTT service with default configuration."""
        return cls()
    
    def __enter__(self):
        """Context manager entry."""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop()


# Global service instance (singleton pattern)
_mqtt_service_instance: Optional[MQTTService] = None


def get_mqtt_service() -> MQTTService:
    """Get the global MQTT service instance."""
    global _mqtt_service_instance
    if _mqtt_service_instance is None:
        _mqtt_service_instance = MQTTService.create_default()
    return _mqtt_service_instance


def initialize_mqtt_service(config: Optional[MQTTConfig] = None) -> MQTTService:
    """Initialize the global MQTT service with custom configuration."""
    global _mqtt_service_instance
    _mqtt_service_instance = MQTTService(mqtt_config=config)
    return _mqtt_service_instance
