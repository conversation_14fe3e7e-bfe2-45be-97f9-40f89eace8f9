ℹ️ 2025-09-25 11:03:40.475 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:08:02.597 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:13:19.825 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:13:47.274 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:16:38.275 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:18:23.337 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:19:47.677 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:21:03.200 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:21:21.946 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:21:42.132 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:22:12.719 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:22:27.169 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:25:13.593 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
❌ 2025-09-25 11:25:57.669 - mqtt.service - ERROR - [unknown] Publisher not available or not connected
ℹ️ 2025-09-25 11:25:57.698 - mqtt.service - INFO - [unknown] Starting MQTT service...
ℹ️ 2025-09-25 11:25:57.699 - mqtt.pricepublisher - INFO - [price_publisher] Connecting to MQTT broker at api.smarteye.smartflowtech.org:1883
ℹ️ 2025-09-25 11:25:58.253 - mqtt.pricepublisher - INFO - [unknown] Connection established (client_id=price_publisher, broker=api.smarteye.smartflowtech.org:1883)
ℹ️ 2025-09-25 11:25:58.254 - mqtt.pricepublisher - INFO - [price_publisher] Price publisher connected and ready to send messages
ℹ️ 2025-09-25 11:25:58.313 - mqtt.service - INFO - [unknown] ✅ Publisher connected
ℹ️ 2025-09-25 11:25:58.314 - mqtt.service - INFO - [unknown] Health check started
ℹ️ 2025-09-25 11:25:58.315 - mqtt.service - INFO - [unknown] 🚀 MQTT service started successfully
ℹ️ 2025-09-25 11:25:58.315 - mqtt.pricepublisher - INFO - [price_publisher] Published message to topic 'price_changes': {
  "site_id": "326",
  "new_price": 2000.0,
  "currency": "NGN",
  "timestamp": "2025-09-25T11:25:5...
ℹ️ 2025-09-25 11:35:18.227 - mqtt.pricepublisher - INFO - [price_publisher] Published message to topic 'price_changes': {
  "site_id": "1",
  "new_price": 1500.0,
  "currency": "NGN",
  "timestamp": "2025-09-25T11:35:18....
ℹ️ 2025-09-25 11:52:27.559 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:52:48.252 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:53:13.326 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:53:27.797 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:53:56.907 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:57:18.757 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
❌ 2025-09-25 11:58:20.660 - mqtt.utils - ERROR - [unknown] MQTT publisher not available
❌ 2025-09-25 11:58:20.752 - mqtt.service - ERROR - [unknown] Publisher not available or not connected
ℹ️ 2025-09-25 11:58:20.863 - mqtt.service - INFO - [unknown] Starting MQTT service...
ℹ️ 2025-09-25 11:58:20.865 - mqtt.pricepublisher - INFO - [price_publisher] Connecting to MQTT broker at api.smarteye.smartflowtech.org:1883
ℹ️ 2025-09-25 11:58:21.438 - mqtt.pricepublisher - INFO - [unknown] Connection established (client_id=price_publisher, broker=api.smarteye.smartflowtech.org:1883)
ℹ️ 2025-09-25 11:58:21.438 - mqtt.pricepublisher - INFO - [price_publisher] Price publisher connected and ready to send messages
ℹ️ 2025-09-25 11:58:21.487 - mqtt.service - INFO - [unknown] ✅ Publisher connected
ℹ️ 2025-09-25 11:58:21.488 - mqtt.service - INFO - [unknown] Health check started
ℹ️ 2025-09-25 11:58:21.488 - mqtt.service - INFO - [unknown] 🚀 MQTT service started successfully
ℹ️ 2025-09-25 11:58:21.489 - mqtt.pricepublisher - INFO - [price_publisher] Published message to topic 'price_changes': {
  "site_id": "1",
  "new_price": 500.0,
  "currency": "NGN",
  "timestamp": "2025-09-25T11:58:20.7...
ℹ️ 2025-09-25 12:00:08.053 - mqtt.pricepublisher - INFO - [price_publisher] Published message to topic 'price_change/Pic-00002': {
  "type": "remote_config",
  "timestamp": "2025-09-25T12:00:08.052330",
  "site_id": "234",
  "dev...
ℹ️ 2025-09-25 12:00:08.054 - mqtt.utils - INFO - [unknown] ✅ Sent config to device Pic-00002 via MQTT
ℹ️ 2025-09-25 12:00:08.157 - mqtt.pricepublisher - INFO - [price_publisher] Published message to topic 'price_change/Pic-00007': {
  "type": "remote_config",
  "timestamp": "2025-09-25T12:00:08.157189",
  "site_id": "234",
  "dev...
ℹ️ 2025-09-25 12:00:08.158 - mqtt.utils - INFO - [unknown] ✅ Sent config to device Pic-00007 via MQTT
ℹ️ 2025-09-25 12:00:08.159 - mqtt.utils - INFO - [unknown] 📡 MQTT config sent to 2/2 devices for site 234
ℹ️ 2025-09-25 12:00:08.163 - mqtt.pricepublisher - INFO - [price_publisher] Published message to topic 'price_changes': {
  "site_id": "234",
  "new_price": 300.0,
  "currency": "NGN",
  "timestamp": "2025-09-25T12:00:08...
ℹ️ 2025-09-25 12:07:30.409 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
❌ 2025-09-25 12:08:15.848 - mqtt.utils - ERROR - [unknown] MQTT publisher not available
❌ 2025-09-25 12:08:15.880 - mqtt.service - ERROR - [unknown] Publisher not available or not connected
ℹ️ 2025-09-25 12:08:15.906 - mqtt.service - INFO - [unknown] Starting MQTT service...
ℹ️ 2025-09-25 12:08:15.906 - mqtt.pricepublisher - INFO - [price_publisher] Connecting to MQTT broker at api.smarteye.smartflowtech.org:1883
ℹ️ 2025-09-25 12:08:16.720 - mqtt.pricepublisher - INFO - [unknown] Connection established (client_id=price_publisher, broker=api.smarteye.smartflowtech.org:1883)
ℹ️ 2025-09-25 12:08:16.721 - mqtt.pricepublisher - INFO - [price_publisher] Price publisher connected and ready to send messages
ℹ️ 2025-09-25 12:08:16.771 - mqtt.service - INFO - [unknown] ✅ Publisher connected
ℹ️ 2025-09-25 12:08:16.774 - mqtt.service - INFO - [unknown] Health check started
ℹ️ 2025-09-25 12:08:16.776 - mqtt.service - INFO - [unknown] 🚀 MQTT service started successfully
ℹ️ 2025-09-25 12:08:16.780 - mqtt.pricepublisher - INFO - [price_publisher] Published message to topic 'price_change/Pic-00002': {
  "site_id": "234",
  "new_price": 600.0,
  "currency": "NGN",
  "timestamp": "2025-09-25T12:08:15...
ℹ️ 2025-09-25 12:12:44.088 - mqtt.pricepublisher - INFO - [price_publisher] Published message to topic 'price_change/Pic-00002': {
  "type": "remote_config",
  "timestamp": "2025-09-25T12:12:44.086540",
  "site_id": "234",
  "dev...
ℹ️ 2025-09-25 12:12:44.088 - mqtt.utils - INFO - [unknown] ✅ Sent config to device Pic-00002 via MQTT
ℹ️ 2025-09-25 12:12:44.248 - mqtt.pricepublisher - INFO - [price_publisher] Published message to topic 'price_change/Pic-00007': {
  "type": "remote_config",
  "timestamp": "2025-09-25T12:12:44.247628",
  "site_id": "234",
  "dev...
ℹ️ 2025-09-25 12:12:44.249 - mqtt.utils - INFO - [unknown] ✅ Sent config to device Pic-00007 via MQTT
ℹ️ 2025-09-25 12:12:44.249 - mqtt.utils - INFO - [unknown] 📡 MQTT config sent to 2/2 devices for site 234
ℹ️ 2025-09-25 12:12:44.259 - mqtt.pricepublisher - INFO - [price_publisher] Published message to topic 'price_change/Pic-00002': {
  "site_id": "234",
  "new_price": 900.0,
  "currency": "NGN",
  "timestamp": "2025-09-25T12:12:44...
