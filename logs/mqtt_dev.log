ℹ️ 2025-09-25 11:03:40.475 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:08:02.597 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:13:19.825 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:13:47.274 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:16:38.275 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:18:23.337 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:19:47.677 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:21:03.200 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:21:21.946 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:21:42.132 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:22:12.719 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:22:27.169 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
ℹ️ 2025-09-25 11:25:13.593 - mqtt - INFO - [unknown] MQTT logging initialized - Level: DEBUG, File: logs/mqtt_dev.log
❌ 2025-09-25 11:25:57.669 - mqtt.service - ERROR - [unknown] Publisher not available or not connected
ℹ️ 2025-09-25 11:25:57.698 - mqtt.service - INFO - [unknown] Starting MQTT service...
ℹ️ 2025-09-25 11:25:57.699 - mqtt.pricepublisher - INFO - [price_publisher] Connecting to MQTT broker at api.smarteye.smartflowtech.org:1883
ℹ️ 2025-09-25 11:25:58.253 - mqtt.pricepublisher - INFO - [unknown] Connection established (client_id=price_publisher, broker=api.smarteye.smartflowtech.org:1883)
ℹ️ 2025-09-25 11:25:58.254 - mqtt.pricepublisher - INFO - [price_publisher] Price publisher connected and ready to send messages
ℹ️ 2025-09-25 11:25:58.313 - mqtt.service - INFO - [unknown] ✅ Publisher connected
ℹ️ 2025-09-25 11:25:58.314 - mqtt.service - INFO - [unknown] Health check started
ℹ️ 2025-09-25 11:25:58.315 - mqtt.service - INFO - [unknown] 🚀 MQTT service started successfully
ℹ️ 2025-09-25 11:25:58.315 - mqtt.pricepublisher - INFO - [price_publisher] Published message to topic 'price_changes': {
  "site_id": "326",
  "new_price": 2000.0,
  "currency": "NGN",
  "timestamp": "2025-09-25T11:25:5...
ℹ️ 2025-09-25 11:35:18.227 - mqtt.pricepublisher - INFO - [price_publisher] Published message to topic 'price_changes': {
  "site_id": "1",
  "new_price": 1500.0,
  "currency": "NGN",
  "timestamp": "2025-09-25T11:35:18....
